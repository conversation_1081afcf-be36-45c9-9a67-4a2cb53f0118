然后还缺少一个sql的一个管理类，你可以看看物品她是有一个sql的管理类的，然后是你把一些sql的字符串放到了这个日志类中，我希望你作出改变，然后我希望有一个测试类，这个测试类是这样的，她可以就是显示原始的内容，而不是你解析后的内容，测试怪物1002,显示她的完整信息给我
然后是关于redis的，redis控制类当中，需要提供一个方法，这个方法会清除掉，你当前获取全部数据的缓存，但是不会影响到其他的缓存，就是单独删除掉这个模块的，比如说我的怪物里面有一个可以获取分类列表，和一个获取全部信息就是你现在正在是实现的这个，但是这个方法她只是会清理掉当前获取全部数据的缓存而不会清理掉分类的缓存




我还发现一个问题就是怪物数据处理类还是定义了很多字段的结构体，我不知掉这是不是rust语言的硬性要求，但是在结构体类已经定义了这些，有没有一个方法，可以直接调用，不需要定义
match row {
Some(row) => {
Ok(guaiwu_huizong_xinxi {
id: row.try_get::<Option<String>, _>("id").unwrap_or(None),
dbname: row.try_get::<Option<String>, _>("dbname").unwrap_or(None),
zhongwenming: row.try_get::<Option<String>, _>("zhongwenming").unwrap_or(None),
chicun_small: row.try_get::<Option<String>, _>("chicun_small").unwrap_or(None),
chicun_medium: row.try_get::<Option<String>, _>("chicun_medium").unwrap_or(None),
chicun_large: row.try_get::<Option<String>, _>("chicun_large").unwrap_or(None),
chicun_weizhi: row.try_get::<Option<String>, _>("chicun_weizhi").unwrap_or(None),
yuansu_wu: row.try_get::<Option<String>, _>("yuansu_wu").unwrap_or(None),
yuansu_shui: row.try_get::<Option<String>, _>("yuansu_shui").unwrap_or(None),
yuansu_di: row.try_get::<Option<String>, _>("yuansu_di").unwrap_or(None),
yuansu_huo: row.try_get::<Option<String>, _>("yuansu_huo").unwrap_or(None),
yuansu_feng: row.try_get::<Option<String>, _>("yuansu_feng").unwrap_or(None),
yuansu_du: row.try_get::<Option<String>, _>("yuansu_du").unwrap_or(None),
yuansu_sheng: row.try_get::<Option<String>, _>("yuansu_sheng").unwrap_or(None),
......
这么多的一个结构体了？如果有你需要做出修改？
并且你需要告诉我，这个方法是否符合我的需求，首先是redis的缓存，她只会缓存获取全部信息，也就是name表和huizong表关于这个怪物，通过id来索引，就是关于这个怪物的全部信息，但是她不会缓存就是我查询这个怪物单个字段的信息，这是不会换缓存的！
然后是单个字段的查询，她不允许查询name表中的字段，只允许查询huizong表中的字段，但是如果是直接查询全部信息的话，她就可以两个表都进行一个id的索引然后返回两个表关于这个怪物的信息