#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use super::guaiwujiegouti::{
    guaiwu_jiben_xinxi, guaiwu_huizong_xinxi, guaiwu_wanzheng_xinxi,
    guaiwu_chaxun_jieguo, guaiwu_ziduan_yingshe
};
use super::guaiwu_redis_kongzhi::guaiwu_redis_kongzhi;
use super::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use super::guaiwu_sql_kongzhi::guaiwu_sql_guanli;
use sqlx::Row;
use std::collections::HashMap;

/// 怪物数据管理器
pub struct guaiwu_shuju_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
    redis_kongzhi: Option<guaiwu_redis_kongzhi>,
}

impl guaiwu_shuju_guanli {
    /// 创建新的怪物数据管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: None,
        }
    }

    /// 创建带Redis缓存的怪物数据管理器实例
    pub fn new_with_redis(mysql_lianjie: mysql_lianjie_guanli, redis_kongzhi: guaiwu_redis_kongzhi) -> Self {
        Self {
            mysql_lianjie,
            redis_kongzhi: Some(redis_kongzhi),
        }
    }

    /// 通用查询方法
    ///
    /// # 参数
    /// * `guaiwu_id` - 怪物ID
    /// * `chaxun_moshi` - 查询模式：
    ///   - "quanbu_xinxi": 查询全部信息（同时查询mob_name表和guaiwu_huizong表）
    ///   - 字段名列表（用逗号分隔）: 只查询guaiwu_huizong表的指定字段
    pub async fn tongyong_chaxun(&self, guaiwu_id: &str, chaxun_moshi: &str) -> anyhow::Result<guaiwu_chaxun_jieguo> {
        if chaxun_moshi == guaiwu_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi {
            self.chaxun_quanbu_xinxi(guaiwu_id).await
        } else {
            self.chaxun_zhiding_ziduan(guaiwu_id, chaxun_moshi).await
        }
    }

    /// 查询怪物全部信息（带Redis缓存）
    async fn chaxun_quanbu_xinxi(&self, guaiwu_id: &str) -> anyhow::Result<guaiwu_chaxun_jieguo> {
        // 如果有Redis控制器，先尝试从Redis获取
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            if let Ok(Some(wanzheng_xinxi)) = redis_kongzhi.huoqu_quanbu_xinxi(guaiwu_id).await {
                return Ok(guaiwu_chaxun_jieguo {
                    chenggong: true,
                    cuowu_xinxi: None,
                    wanzheng_xinxi: Some(wanzheng_xinxi),
                    guaiwu_shuju: None,
                });
            }
        }

        // Redis中没有数据，从MySQL查询
        let jiben_xinxi = match self.chaxun_jiben_xinxi(guaiwu_id).await {
            Ok(xinxi) => Some(xinxi),
            Err(_) => None,
        };

        let huizong_xinxi = match self.chaxun_huizong_xinxi(guaiwu_id).await {
            Ok(xinxi) => Some(xinxi),
            Err(_) => None,
        };

        // 检查是否至少有一个表有数据
        if jiben_xinxi.is_none() && huizong_xinxi.is_none() {
            return Ok(guaiwu_chaxun_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_guaiwu_bucunzai(guaiwu_id)),
                wanzheng_xinxi: None,
                guaiwu_shuju: None,
            });
        }

        let wanzheng_xinxi = guaiwu_wanzheng_xinxi {
            id: guaiwu_id.to_string(),
            jiben_xinxi,
            huizong_xinxi,
        };

        // 如果有Redis控制器，将查询结果存储到Redis
        if let Some(redis_kongzhi) = &self.redis_kongzhi {
            let _ = redis_kongzhi.cunchu_quanbu_xinxi(guaiwu_id, &wanzheng_xinxi).await;
        }

        Ok(guaiwu_chaxun_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            wanzheng_xinxi: Some(wanzheng_xinxi),
            guaiwu_shuju: None,
        })
    }

    /// 查询指定字段（只查询汇总表）
    async fn chaxun_zhiding_ziduan(&self, guaiwu_id: &str, ziduan_liebiao: &str) -> anyhow::Result<guaiwu_chaxun_jieguo> {
        let ziduan_mingcheng: Vec<&str> = ziduan_liebiao.split(',').map(|s| s.trim()).collect();

        // 验证字段是否都在汇总表中
        let huizong_biao_ziduan = guaiwu_ziduan_yingshe::huoqu_huizong_biao_ziduan();
        let mut youxiao_ziduan = Vec::new();

        for ziduan in &ziduan_mingcheng {
            if huizong_biao_ziduan.contains(&ziduan.to_string()) {
                youxiao_ziduan.push(*ziduan);
            }
        }

        if youxiao_ziduan.is_empty() {
            return Ok(guaiwu_chaxun_jieguo {
                chenggong: false,
                cuowu_xinxi: Some(guaiwu_zifuchuan_changliangguanli::cuowu_ziduan_bu_zai_huizong_biao.to_string()),
                wanzheng_xinxi: None,
                guaiwu_shuju: None,
            });
        }

        // 只查询汇总表字段
        match self.chaxun_huizong_biao_ziduan(guaiwu_id, &youxiao_ziduan).await {
            Ok(huizong_shuju) => {
                if huizong_shuju.is_empty() {
                    Ok(guaiwu_chaxun_jieguo {
                        chenggong: false,
                        cuowu_xinxi: Some(guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_guaiwu_zai_huizong_biao_bucunzai(guaiwu_id)),
                        wanzheng_xinxi: None,
                        guaiwu_shuju: None,
                    })
                } else {
                    Ok(guaiwu_chaxun_jieguo {
                        chenggong: true,
                        cuowu_xinxi: None,
                        wanzheng_xinxi: None,
                        guaiwu_shuju: Some(huizong_shuju),
                    })
                }
            }
            Err(_) => {
                Ok(guaiwu_chaxun_jieguo {
                    chenggong: false,
                    cuowu_xinxi: Some(guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_guaiwu_zai_huizong_biao_bucunzai(guaiwu_id)),
                    wanzheng_xinxi: None,
                    guaiwu_shuju: None,
                })
            }
        }
    }

    /// 查询mob_name表基础信息
    async fn chaxun_jiben_xinxi(&self, guaiwu_id: &str) -> anyhow::Result<guaiwu_jiben_xinxi> {
        let jiben_xinxi = sqlx::query_as::<_, guaiwu_jiben_xinxi>(guaiwu_sql_guanli::sql_chaxun_jiben_xinxi)
            .bind(guaiwu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match jiben_xinxi {
            Some(xinxi) => Ok(xinxi),
            None => Err(anyhow::anyhow!(guaiwu_zifuchuan_changliangguanli::cuowu_guaiwu_jiben_xinxi_bucunzai))
        }
    }

    /// 查询guaiwu_huizong表汇总信息
    async fn chaxun_huizong_xinxi(&self, guaiwu_id: &str) -> anyhow::Result<guaiwu_huizong_xinxi> {
        let huizong_xinxi = sqlx::query_as::<_, guaiwu_huizong_xinxi>(guaiwu_sql_guanli::sql_chaxun_huizong_xinxi)
            .bind(guaiwu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match huizong_xinxi {
            Some(xinxi) => Ok(xinxi),
            None => Err(anyhow::anyhow!(guaiwu_zifuchuan_changliangguanli::cuowu_guaiwu_huizong_xinxi_bucunzai))
        }
    }

    /// 查询mob_name表指定字段
    async fn chaxun_jiben_biao_ziduan(&self, guaiwu_id: &str, ziduan_liebiao: &[&str]) -> anyhow::Result<HashMap<String, String>> {
        if ziduan_liebiao.is_empty() {
            return Ok(HashMap::new());
        }

        let sql = guaiwu_sql_guanli::shengcheng_sql_chaxun_jiben_biao_ziduan(ziduan_liebiao);

        let row = sqlx::query(&sql)
            .bind(guaiwu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                let mut jieguo = HashMap::new();
                for ziduan in ziduan_liebiao {
                    let zhi = row.try_get::<Option<String>, _>(*ziduan)
                        .unwrap_or(None)
                        .unwrap_or_default();
                    jieguo.insert(ziduan.to_string(), zhi);
                }
                Ok(jieguo)
            }
            None => Ok(HashMap::new())
        }
    }

    /// 查询guaiwu_huizong表指定字段
    async fn chaxun_huizong_biao_ziduan(&self, guaiwu_id: &str, ziduan_liebiao: &[&str]) -> anyhow::Result<HashMap<String, String>> {
        if ziduan_liebiao.is_empty() {
            return Ok(HashMap::new());
        }

        let sql = guaiwu_sql_guanli::shengcheng_sql_chaxun_huizong_biao_ziduan(ziduan_liebiao);

        let row = sqlx::query(&sql)
            .bind(guaiwu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                let mut jieguo = HashMap::new();
                for ziduan in ziduan_liebiao {
                    let zhi = row.try_get::<Option<String>, _>(*ziduan)
                        .unwrap_or(None)
                        .unwrap_or_default();
                    jieguo.insert(ziduan.to_string(), zhi);
                }
                Ok(jieguo)
            }
            None => Ok(HashMap::new())
        }
    }

    /// 批量查询怪物
    pub async fn piliang_chaxun(&self, id_liebiao: Vec<String>, chaxun_moshi: &str) -> anyhow::Result<Vec<guaiwu_chaxun_jieguo>> {
        let mut jieguo_liebiao = Vec::new();

        for id in id_liebiao {
            let jieguo = self.tongyong_chaxun(&id, chaxun_moshi).await?;
            jieguo_liebiao.push(jieguo);
        }

        Ok(jieguo_liebiao)
    }

    /// 检查怪物是否存在
    pub async fn jiancha_guaiwu_cunzai(&self, guaiwu_id: &str) -> anyhow::Result<bool> {
        // 检查mob_name表
        let jiben_jieguo = sqlx::query(guaiwu_sql_guanli::sql_jiancha_jiben_biao_cunzai)
            .bind(guaiwu_id)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let jiben_count: i64 = jiben_jieguo.get("count");
        if jiben_count > 0 {
            return Ok(true);
        }

        // 检查guaiwu_huizong表
        let huizong_jieguo = sqlx::query(guaiwu_sql_guanli::sql_jiancha_huizong_biao_cunzai)
            .bind(guaiwu_id)
            .fetch_one(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        let huizong_count: i64 = huizong_jieguo.get("count");
        Ok(huizong_count > 0)
    }

    /// 获取支持的字段列表
    /// 注意：指定字段查询只支持汇总表字段，全部信息查询支持两个表的字段
    pub fn huoqu_zhichi_ziduan() -> HashMap<String, Vec<String>> {
        let mut ziduan_map = HashMap::new();
        ziduan_map.insert(guaiwu_zifuchuan_changliangguanli::biao_ming_mob_name.to_string(), guaiwu_ziduan_yingshe::huoqu_jiben_biao_ziduan());
        ziduan_map.insert(guaiwu_zifuchuan_changliangguanli::biao_ming_guaiwu_huizong.to_string(), guaiwu_ziduan_yingshe::huoqu_huizong_biao_ziduan());
        ziduan_map.insert(guaiwu_zifuchuan_changliangguanli::biao_ming_zhiding_ziduan_zhichi.to_string(), guaiwu_ziduan_yingshe::huoqu_huizong_biao_ziduan());
        ziduan_map
    }

    /// 清理怪物全部数据获取的Redis缓存
    /// 只清除怪物数据获取相关的缓存，不会清理其他缓存
    pub async fn qingchu_guaiwu_quanbu_xinxi_huancun(&self) -> anyhow::Result<u64> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.qingchu_guaiwu_quanbu_xinxi_huancun().await
            }
            None => Ok(0) // 没有Redis控制器，返回0
        }
    }

    /// 删除指定怪物的全部信息缓存
    pub async fn shanchu_guaiwu_huancun(&self, guaiwu_id: &str) -> anyhow::Result<bool> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.shanchu_quanbu_xinxi(guaiwu_id).await
            }
            None => Ok(false) // 没有Redis控制器，返回false
        }
    }

    /// 获取怪物缓存统计信息
    pub async fn huoqu_guaiwu_huancun_tongji(&self) -> anyhow::Result<String> {
        match &self.redis_kongzhi {
            Some(redis_kongzhi) => {
                redis_kongzhi.huoqu_guaiwu_huancun_tongji().await
            }
            None => Ok(guaiwu_zifuchuan_changliangguanli::tongji_wei_qiyong_redis_huancun.to_string())
        }
    }
}
