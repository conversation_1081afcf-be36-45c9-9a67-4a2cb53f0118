#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

#[cfg(test)]
mod tests {
    use super::super::guaiwujiegouti::*;
    use super::super::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
    use std::collections::HashMap;

    #[test]
    fn test_guaiwu_ziduan_yingshe() {
        // 测试获取基础表字段
        let jiben_ziduan = guaiwu_ziduan_yingshe::huoqu_jiben_biao_ziduan();
        assert!(!jiben_ziduan.is_empty());
        assert!(jiben_ziduan.contains(&"ID".to_string()));
        assert!(jiben_ziduan.contains(&"schinese".to_string()));

        // 测试获取汇总表字段
        let huizong_ziduan = guaiwu_ziduan_yingshe::huoqu_huizong_biao_ziduan();
        assert!(!huizong_ziduan.is_empty());
        assert!(huizong_ziduan.contains(&"id".to_string()));
        assert!(huizong_ziduan.contains(&"zhongwenming".to_string()));
        assert!(huizong_ziduan.contains(&"level".to_string()));

        // 测试字段有效性检查
        assert!(guaiwu_ziduan_yingshe::jiancha_ziduan_youxiao("ID"));
        assert!(guaiwu_ziduan_yingshe::jiancha_ziduan_youxiao("zhongwenming"));
        assert!(!guaiwu_ziduan_yingshe::jiancha_ziduan_youxiao("invalid_field"));
    }

    #[test]
    fn test_guaiwu_jiben_xinxi_creation() {
        let jiben_xinxi = guaiwu_jiben_xinxi {
            id: "1001".to_string(),
            aegis_name: Some("PORING".to_string()),
            type_field: Some("Monster".to_string()),
            schinese: Some("波利".to_string()),
            tchinese: Some("波利".to_string()),
            en: Some("Poring".to_string()),
            jp: Some("ポリン".to_string()),
            kr: Some("포링".to_string()),
        };

        assert_eq!(jiben_xinxi.id, "1001");
        assert_eq!(jiben_xinxi.schinese, Some("波利".to_string()));
    }

    #[test]
    fn test_guaiwu_huizong_xinxi_creation() {
        let huizong_xinxi = guaiwu_huizong_xinxi {
            id: Some("1001".to_string()),
            dbname: Some("PORING".to_string()),
            zhongwenming: Some("波利".to_string()),
            level: Some("1".to_string()),
            health: Some("50".to_string()),
            base_experience: Some("2".to_string()),
            job_experience: Some("1".to_string()),
            mvp_flag: Some("0".to_string()),
            // 其他字段设为None以简化测试
            chicun_small: None,
            chicun_medium: None,
            chicun_large: None,
            chicun_weizhi: None,
            yuansu_wu: None,
            yuansu_shui: None,
            yuansu_di: None,
            yuansu_huo: None,
            yuansu_feng: None,
            yuansu_du: None,
            yuansu_sheng: None,
            yuansu_an: None,
            yuansu_nian: None,
            yuansu_busi: None,
            yuansu_weizhi: None,
            zhongzu_formless: None,
            zhongzu_undead: None,
            zhongzu_brute: None,
            zhongzu_plant: None,
            zhongzu_insect: None,
            zhongzu_fish: None,
            zhongzu_demon: None,
            zhongzu_human: None,
            zhongzu_angel: None,
            zhongzu_dragon: None,
            zhongzu_weizhi: None,
            biaozhi_normal: None,
            biaozhi_champion: None,
            biaozhi_boss: None,
            biaozhi_mvp: None,
            biaozhi_weizhi: None,
            ai_aggressive: None,
            ai_assist: None,
            ai_looter: None,
            ai_cast_sensor: None,
            ai_immobile: None,
            ai_weizhi: None,
            jichuxinxi_yaml: None,
            zhushuxing_yaml: None,
            ai_behaviors_yaml: None,
            yuansukangxing_yaml: None,
            fenlei_xinxi_yaml: None,
            putong_diaoluowu_yaml: None,
            mvp_diaoluowu_yaml: None,
            ditu_diaoluowu_yaml: None,
            jineng_liebiao_yaml: None,
            ditu_liebiao_yaml: None,
            jingyan_xinxi_yaml: None,
            renwu_liebiao_yaml: None,
            chongwu_xinxi_yaml: None,
            diaoluowu_shuliang: None,
            jineng_shuliang: None,
            ditu_shuliang: None,
            renwu_shuliang: None,
            shi_chongwu: None,
            you_jichuxinxi: None,
            you_diaoluowu: None,
            you_jineng: None,
            you_ditu: None,
            you_jingyan: None,
            you_renwu: None,
            you_chongwu: None,
            jingyingguai: None,
        };

        assert_eq!(huizong_xinxi.id, Some("1001".to_string()));
        assert_eq!(huizong_xinxi.zhongwenming, Some("波利".to_string()));
        assert_eq!(huizong_xinxi.level, Some("1".to_string()));
    }

    #[test]
    fn test_guaiwu_wanzheng_xinxi_creation() {
        let jiben_xinxi = guaiwu_jiben_xinxi {
            id: "1001".to_string(),
            aegis_name: Some("PORING".to_string()),
            type_field: Some("Monster".to_string()),
            schinese: Some("波利".to_string()),
            tchinese: None,
            en: None,
            jp: None,
            kr: None,
        };

        let wanzheng_xinxi = guaiwu_wanzheng_xinxi {
            id: "1001".to_string(),
            jiben_xinxi: Some(jiben_xinxi),
            huizong_xinxi: None,
        };

        assert_eq!(wanzheng_xinxi.id, "1001");
        assert!(wanzheng_xinxi.jiben_xinxi.is_some());
        assert!(wanzheng_xinxi.huizong_xinxi.is_none());
    }

    #[test]
    fn test_guaiwu_chaxun_jieguo_creation() {
        let mut shuju_map = HashMap::new();
        shuju_map.insert("id".to_string(), "1001".to_string());
        shuju_map.insert("zhongwenming".to_string(), "波利".to_string());

        let jieguo = guaiwu_chaxun_jieguo {
            chenggong: true,
            cuowu_xinxi: None,
            wanzheng_xinxi: None,
            guaiwu_shuju: Some(shuju_map),
        };

        assert!(jieguo.chenggong);
        assert!(jieguo.cuowu_xinxi.is_none());
        assert!(jieguo.guaiwu_shuju.is_some());
    }

    #[test]
    fn test_guaiwu_zifuchuan_changliangguanli() {
        // 测试常量
        assert_eq!(guaiwu_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi, "quanbu_xinxi");
        assert_eq!(guaiwu_zifuchuan_changliangguanli::biao_ming_mob_name, "mob_name");
        assert_eq!(guaiwu_zifuchuan_changliangguanli::biao_ming_guaiwu_huizong, "guaiwu_huizong");

        // 测试错误信息生成
        let cuowu_xinxi = guaiwu_zifuchuan_changliangguanli::shengcheng_cuowu_guaiwu_bucunzai("1001");
        assert!(cuowu_xinxi.contains("1001"));

        // 测试Redis键名生成
        let redis_jian = guaiwu_zifuchuan_changliangguanli::shengcheng_redis_jian_guaiwu_quanbu("1001");
        assert!(redis_jian.contains("1001"));
        assert!(redis_jian.contains("guaiwu_quanbu"));

        // 测试SQL生成
        let sql = guaiwu_zifuchuan_changliangguanli::shengcheng_sql_chaxun_jiben_biao_ziduan(&["ID", "schinese"]);
        assert!(sql.contains("ID"));
        assert!(sql.contains("schinese"));
        assert!(sql.contains("mob_name"));

        let sql2 = guaiwu_zifuchuan_changliangguanli::shengcheng_sql_chaxun_huizong_biao_ziduan(&["id", "zhongwenming"]);
        assert!(sql2.contains("id"));
        assert!(sql2.contains("zhongwenming"));
        assert!(sql2.contains("guaiwu_huizong"));
    }
}
