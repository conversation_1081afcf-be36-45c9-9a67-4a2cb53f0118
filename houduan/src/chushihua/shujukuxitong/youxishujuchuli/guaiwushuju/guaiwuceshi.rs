#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use crate::chushihua::shujukuxitong::mysqlshujuku::mysql_lianjie::mysql_lianjie_guanli;
use super::guaiwushujuchuli::guaiwu_shuju_guanli;
use super::guaiwu_sql_kongzhi::guaiwu_sql_guanli;
use super::guaiwu_rizhi_kongzhi::guaiwu_zifuchuan_changliangguanli;
use sqlx::Row;

/// 怪物数据测试类 - 显示原始数据
pub struct guaiwu_ceshi_guanli {
    mysql_lianjie: mysql_lianjie_guanli,
}

impl guaiwu_ceshi_guanli {
    /// 创建新的怪物测试管理器实例
    pub fn new(mysql_lianjie: mysql_lianjie_guanli) -> Self {
        Self {
            mysql_lianjie,
        }
    }

    /// 测试显示怪物完整信息（原始数据）
    pub async fn ceshi_xianshi_guaiwu_wanzheng_xinxi(&self, guaiwu_id: &str) -> anyhow::Result<()> {
        println!("=== 怪物ID: {} 的完整信息测试 ===", guaiwu_id);
        println!();

        // 查询mob_name表原始数据
        println!("--- mob_name表原始数据 ---");
        match self.chaxun_mob_name_yuanshi_shuju(guaiwu_id).await {
            Ok(()) => {},
            Err(e) => {
                println!("mob_name表查询失败: {}", e);
            }
        }
        println!();

        // 查询guaiwu_huizong表原始数据
        println!("--- guaiwu_huizong表原始数据 ---");
        match self.chaxun_guaiwu_huizong_yuanshi_shuju(guaiwu_id).await {
            Ok(()) => {},
            Err(e) => {
                println!("guaiwu_huizong表查询失败: {}", e);
            }
        }
        println!();

        // 使用怪物数据管理器查询解析后的数据进行对比
        println!("--- 解析后的数据（用于对比） ---");
        let guaiwu_guanli = guaiwu_shuju_guanli::new(self.mysql_lianjie.clone());
        match guaiwu_guanli.tongyong_chaxun(guaiwu_id, guaiwu_zifuchuan_changliangguanli::chaxun_moshi_quanbu_xinxi).await {
            Ok(jieguo) => {
                if jieguo.chenggong {
                    if let Some(wanzheng_xinxi) = jieguo.wanzheng_xinxi {
                        println!("解析成功:");
                        println!("  怪物ID: {}", wanzheng_xinxi.id);
                        
                        if let Some(jiben_xinxi) = wanzheng_xinxi.jiben_xinxi {
                            println!("  基础信息:");
                            println!("    Aegis名称: {:?}", jiben_xinxi.aegis_name);
                            println!("    简体中文: {:?}", jiben_xinxi.schinese);
                            println!("    繁体中文: {:?}", jiben_xinxi.tchinese);
                        }
                        
                        if let Some(huizong_xinxi) = wanzheng_xinxi.huizong_xinxi {
                            println!("  汇总信息:");
                            println!("    中文名: {:?}", huizong_xinxi.zhongwenming);
                            println!("    等级: {:?}", huizong_xinxi.level);
                            println!("    生命值: {:?}", huizong_xinxi.health);
                            println!("    基础经验: {:?}", huizong_xinxi.base_experience);
                            println!("    职业经验: {:?}", huizong_xinxi.job_experience);
                            println!("    MVP标记: {:?}", huizong_xinxi.mvp_flag);
                        }
                    }
                } else {
                    println!("解析失败: {:?}", jieguo.cuowu_xinxi);
                }
            }
            Err(e) => {
                println!("查询失败: {}", e);
            }
        }

        println!();
        println!("=== 测试完成 ===");
        Ok(())
    }

    /// 查询mob_name表原始数据
    async fn chaxun_mob_name_yuanshi_shuju(&self, guaiwu_id: &str) -> anyhow::Result<()> {
        let row = sqlx::query(guaiwu_sql_guanli::sql_chaxun_jiben_xinxi)
            .bind(guaiwu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                println!("找到数据:");
                
                // 获取所有列的原始值
                let id: Result<String, _> = row.try_get("ID");
                let aegis_name: Result<Option<String>, _> = row.try_get("Aegis_name");
                let type_field: Result<Option<String>, _> = row.try_get("Type");
                let schinese: Result<Option<String>, _> = row.try_get("schinese");
                let tchinese: Result<Option<String>, _> = row.try_get("tchinese");
                let en: Result<Option<String>, _> = row.try_get("en");
                let jp: Result<Option<String>, _> = row.try_get("jp");
                let kr: Result<Option<String>, _> = row.try_get("kr");

                println!("  ID: {:?}", id);
                println!("  Aegis_name: {:?}", aegis_name);
                println!("  Type: {:?}", type_field);
                println!("  schinese: {:?}", schinese);
                println!("  tchinese: {:?}", tchinese);
                println!("  en: {:?}", en);
                println!("  jp: {:?}", jp);
                println!("  kr: {:?}", kr);
            }
            None => {
                println!("未找到数据");
            }
        }
        Ok(())
    }

    /// 查询guaiwu_huizong表原始数据
    async fn chaxun_guaiwu_huizong_yuanshi_shuju(&self, guaiwu_id: &str) -> anyhow::Result<()> {
        let row = sqlx::query(guaiwu_sql_guanli::sql_chaxun_huizong_xinxi)
            .bind(guaiwu_id)
            .fetch_optional(self.mysql_lianjie.huoqu_lianjiechi()?)
            .await?;

        match row {
            Some(row) => {
                println!("找到数据:");
                
                // 显示主要字段的原始值
                let id: Result<Option<String>, _> = row.try_get("id");
                let dbname: Result<Option<String>, _> = row.try_get("dbname");
                let zhongwenming: Result<Option<String>, _> = row.try_get("zhongwenming");
                let level: Result<Option<String>, _> = row.try_get("level");
                let health: Result<Option<String>, _> = row.try_get("health");
                let base_experience: Result<Option<String>, _> = row.try_get("base_experience");
                let job_experience: Result<Option<String>, _> = row.try_get("job_experience");
                let mvp_flag: Result<Option<String>, _> = row.try_get("mvp_flag");

                println!("  id: {:?}", id);
                println!("  dbname: {:?}", dbname);
                println!("  zhongwenming: {:?}", zhongwenming);
                println!("  level: {:?}", level);
                println!("  health: {:?}", health);
                println!("  base_experience: {:?}", base_experience);
                println!("  job_experience: {:?}", job_experience);
                println!("  mvp_flag: {:?}", mvp_flag);

                // 显示尺寸相关字段
                println!("  尺寸信息:");
                let chicun_small: Result<Option<String>, _> = row.try_get("chicun_small");
                let chicun_medium: Result<Option<String>, _> = row.try_get("chicun_medium");
                let chicun_large: Result<Option<String>, _> = row.try_get("chicun_large");
                let chicun_weizhi: Result<Option<String>, _> = row.try_get("chicun_weizhi");
                println!("    chicun_small: {:?}", chicun_small);
                println!("    chicun_medium: {:?}", chicun_medium);
                println!("    chicun_large: {:?}", chicun_large);
                println!("    chicun_weizhi: {:?}", chicun_weizhi);

                // 显示元素相关字段
                println!("  元素信息:");
                let yuansu_wu: Result<Option<String>, _> = row.try_get("yuansu_wu");
                let yuansu_shui: Result<Option<String>, _> = row.try_get("yuansu_shui");
                let yuansu_di: Result<Option<String>, _> = row.try_get("yuansu_di");
                let yuansu_huo: Result<Option<String>, _> = row.try_get("yuansu_huo");
                let yuansu_feng: Result<Option<String>, _> = row.try_get("yuansu_feng");
                println!("    yuansu_wu: {:?}", yuansu_wu);
                println!("    yuansu_shui: {:?}", yuansu_shui);
                println!("    yuansu_di: {:?}", yuansu_di);
                println!("    yuansu_huo: {:?}", yuansu_huo);
                println!("    yuansu_feng: {:?}", yuansu_feng);

                // 显示种族相关字段
                println!("  种族信息:");
                let zhongzu_formless: Result<Option<String>, _> = row.try_get("zhongzu_formless");
                let zhongzu_undead: Result<Option<String>, _> = row.try_get("zhongzu_undead");
                let zhongzu_brute: Result<Option<String>, _> = row.try_get("zhongzu_brute");
                let zhongzu_plant: Result<Option<String>, _> = row.try_get("zhongzu_plant");
                let zhongzu_human: Result<Option<String>, _> = row.try_get("zhongzu_human");
                println!("    zhongzu_formless: {:?}", zhongzu_formless);
                println!("    zhongzu_undead: {:?}", zhongzu_undead);
                println!("    zhongzu_brute: {:?}", zhongzu_brute);
                println!("    zhongzu_plant: {:?}", zhongzu_plant);
                println!("    zhongzu_human: {:?}", zhongzu_human);

                // 显示标志相关字段
                println!("  标志信息:");
                let biaozhi_normal: Result<Option<String>, _> = row.try_get("biaozhi_normal");
                let biaozhi_champion: Result<Option<String>, _> = row.try_get("biaozhi_champion");
                let biaozhi_boss: Result<Option<String>, _> = row.try_get("biaozhi_boss");
                let biaozhi_mvp: Result<Option<String>, _> = row.try_get("biaozhi_mvp");
                println!("    biaozhi_normal: {:?}", biaozhi_normal);
                println!("    biaozhi_champion: {:?}", biaozhi_champion);
                println!("    biaozhi_boss: {:?}", biaozhi_boss);
                println!("    biaozhi_mvp: {:?}", biaozhi_mvp);

                // 显示AI相关字段
                println!("  AI信息:");
                let ai_aggressive: Result<Option<String>, _> = row.try_get("ai_aggressive");
                let ai_assist: Result<Option<String>, _> = row.try_get("ai_assist");
                let ai_looter: Result<Option<String>, _> = row.try_get("ai_looter");
                let ai_immobile: Result<Option<String>, _> = row.try_get("ai_immobile");
                println!("    ai_aggressive: {:?}", ai_aggressive);
                println!("    ai_assist: {:?}", ai_assist);
                println!("    ai_looter: {:?}", ai_looter);
                println!("    ai_immobile: {:?}", ai_immobile);
            }
            None => {
                println!("未找到数据");
            }
        }
        Ok(())
    }

    /// 测试指定字段查询
    pub async fn ceshi_zhiding_ziduan_chaxun(&self, guaiwu_id: &str, ziduan_liebiao: &str) -> anyhow::Result<()> {
        println!("=== 测试指定字段查询: {} ===", ziduan_liebiao);
        println!("怪物ID: {}", guaiwu_id);
        
        let guaiwu_guanli = guaiwu_shuju_guanli::new(self.mysql_lianjie.clone());
        match guaiwu_guanli.tongyong_chaxun(guaiwu_id, ziduan_liebiao).await {
            Ok(jieguo) => {
                if jieguo.chenggong {
                    if let Some(shuju_map) = jieguo.guaiwu_shuju {
                        println!("查询成功，返回字段:");
                        for (ziduan, zhi) in shuju_map {
                            println!("  {}: {}", ziduan, zhi);
                        }
                    }
                } else {
                    println!("查询失败: {:?}", jieguo.cuowu_xinxi);
                }
            }
            Err(e) => {
                println!("查询错误: {}", e);
            }
        }
        println!();
        Ok(())
    }
}
